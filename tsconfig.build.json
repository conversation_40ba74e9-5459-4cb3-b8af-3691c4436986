{"extends": "./tsconfig.json", "compilerOptions": {"skipLibCheck": true, "noEmit": false, "declaration": false, "declarationMap": false, "sourceMap": false, "removeComments": true, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "noUncheckedIndexedAccess": false, "noImplicitOverride": false, "allowUnreachableCode": true, "allowUnusedLabels": true, "exactOptionalPropertyTypes": false, "noPropertyAccessFromIndexSignature": false, "noImplicitThis": false, "useUnknownInCatchVariables": false, "moduleResolution": "node", "outDir": ".wasp/out/user"}, "include": ["src/**/*"], "exclude": ["node_modules", ".wasp", "dist", "build", "**/*.test.*", "**/*.spec.*", "**/test/**", "**/tests/**"]}