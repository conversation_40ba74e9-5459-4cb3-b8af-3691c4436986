#!/bin/bash

echo "🧪 Testing CareerDart Build Process"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "main.wasp" ]; then
    print_error "main.wasp not found. Please run this script from the project root."
    exit 1
fi

print_status "Installing dependencies..."
if ! npm install; then
    print_error "Failed to install dependencies"
    exit 1
fi

print_status "Checking TypeScript configuration..."
if ! npx tsc --noEmit --skipLibCheck; then
    print_warning "TypeScript check failed, but continuing..."
fi

print_status "Testing Wasp build..."
if wasp build; then
    print_success "Wasp build successful!"
    
    # Check if build output exists
    if [ -d ".wasp/build" ]; then
        print_success "Build output directory exists"
        
        # Check for key files
        if [ -f ".wasp/build/web-app/build/index.html" ]; then
            print_success "Main HTML file found"
        else
            print_warning "Main HTML file not found"
        fi
        
        # Show build size
        BUILD_SIZE=$(du -sh .wasp/build/web-app/build/ 2>/dev/null | cut -f1 || echo 'Unknown')
        print_status "Build size: $BUILD_SIZE"
        
    else
        print_error "Build output directory not found"
        exit 1
    fi
else
    print_error "Wasp build failed"
    exit 1
fi

print_success "Build test completed successfully!"
print_status "Your project should be ready for Vercel deployment."
