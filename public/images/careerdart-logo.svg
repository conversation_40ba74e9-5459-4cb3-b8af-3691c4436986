<svg width="200" height="120" viewBox="0 0 200 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="200" height="120" fill="transparent"/>
  
  <!-- CareerDart Text -->
  <text x="100" y="25" font-family="Arial, sans-serif" font-size="18" font-weight="900" text-anchor="middle" fill="#1A202C">
    Career<tspan fill="#3182CE">Dart</tspan>
  </text>
  
  <!-- Tagline -->
  <text x="100" y="40" font-family="Arial, sans-serif" font-size="8" font-weight="600" text-anchor="middle" fill="#718096" letter-spacing="1px">
    YOUR PERSONAL CAREER COMPANION
  </text>
  
  <!-- Logo Circle with Gradient -->
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3182CE;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0891B2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0891B2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3182CE;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Main Logo Circle -->
  <circle cx="100" cy="80" r="30" fill="url(#logoGradient)" filter="url(#glow)" stroke="white" stroke-width="2"/>
  
  <!-- Career Trajectory Path -->
  <path d="M75 105 Q90 85 105 65" stroke="#63B3ED" stroke-width="2" fill="none" stroke-linecap="round" opacity="0.7">
    <animate attributeName="stroke-dasharray" values="0,20;20,0;0,20" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite"/>
  </path>
  
  <!-- Dart Arrow -->
  <g transform="translate(100, 80)">
    <!-- Dart Body -->
    <path d="M8 -8 L15 -15 L13 -17 L6 -10 L4 -12 L2 -10 L8 -4 L10 -6 L8 -8 Z" fill="white" stroke="rgba(255,255,255,0.3)" stroke-width="0.5"/>
    <!-- Dart Tip -->
    <circle cx="13" cy="-13" r="1.5" fill="white"/>
    <!-- Dart Fletching -->
    <path d="M2 -10 L0 -12 L2 -6 L4 -8 Z" fill="rgba(255,255,255,0.8)"/>
    
    <!-- Subtle animation -->
    <animateTransform attributeName="transform" type="translate" values="100,80; 101,79; 100,80" dur="4s" repeatCount="indefinite"/>
  </g>
  
  <!-- Additional decorative elements -->
  <circle cx="70" cy="60" r="2" fill="#63B3ED" opacity="0.4">
    <animate attributeName="opacity" values="0.2;0.6;0.2" dur="2s" repeatCount="indefinite"/>
  </circle>
  <circle cx="130" cy="100" r="1.5" fill="#0891B2" opacity="0.5">
    <animate attributeName="opacity" values="0.3;0.7;0.3" dur="2.5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Success indicators -->
  <circle cx="125" cy="65" r="1" fill="#10B981" opacity="0.6">
    <animate attributeName="r" values="1;2;1" dur="3s" repeatCount="indefinite"/>
  </circle>
</svg>
