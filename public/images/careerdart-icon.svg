<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Logo Circle with Gradient -->
  <defs>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3182CE;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0891B2;stop-opacity:1" />
    </linearGradient>
    <filter id="iconGlow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Main Logo Circle -->
  <circle cx="32" cy="32" r="28" fill="url(#iconGradient)" filter="url(#iconGlow)" stroke="white" stroke-width="2"/>
  
  <!-- Career Trajectory Path -->
  <path d="M15 47 Q25 27 39 17" stroke="#63B3ED" stroke-width="2" fill="none" stroke-linecap="round" opacity="0.7"/>
  
  <!-- Dart Arrow -->
  <g transform="translate(32, 32)">
    <!-- Dart Body -->
    <path d="M6 -6 L12 -12 L10 -14 L4 -8 L2 -10 L0 -8 L6 -2 L8 -4 L6 -6 Z" fill="white" stroke="rgba(255,255,255,0.3)" stroke-width="0.5"/>
    <!-- Dart Tip -->
    <circle cx="10" cy="-10" r="1.2" fill="white"/>
    <!-- Dart Fletching -->
    <path d="M0 -8 L-2 -10 L0 -4 L2 -6 Z" fill="rgba(255,255,255,0.8)"/>
  </g>
  
  <!-- Success indicator -->
  <circle cx="45" cy="19" r="1.5" fill="#10B981" opacity="0.8"/>
</svg>
