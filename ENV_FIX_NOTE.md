# Environment Configuration Fix

## Issue Fixed
The `.env.server` file contained bash-style environment variable syntax that <PERSON><PERSON>'s dotenv parser couldn't understand:

```bash
# This caused an error:
WASP_SERVER_URL=${VERCEL_URL:+https://$VERCEL_URL}
```

## Solution
Reverted to simple environment variable for local development:

```bash
# Fixed version:
WASP_SERVER_URL=http://localhost:3001
```

## For Production Deployment
Vercel will automatically set the production URL via environment variables. No special syntax needed in the .env.server file.

## Status
✅ Local development now works with `wasp start`
✅ Deployment will work with Vercel's automatic environment handling
