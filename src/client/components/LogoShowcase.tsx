import { Box, VStack, HStack, Text, useColorModeValue, Divider } from '@chakra-ui/react';
import Logo from './Logo';

export default function LogoShowcase() {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  return (
    <Box p={8} bg={bgColor} borderRadius="lg" border="1px solid" borderColor={borderColor}>
      <VStack spacing={8} align="stretch">
        <Text fontSize="2xl" fontWeight="bold" textAlign="center">
          CareerDart Logo Variants
        </Text>
        
        <Divider />
        
        {/* Static Logo */}
        <VStack spacing={4}>
          <Text fontSize="lg" fontWeight="semibold">Static Logo</Text>
          <HStack spacing={8} justify="center" wrap="wrap">
            <VStack>
              <Text fontSize="sm" color="gray.500">Small</Text>
              <Logo variant="static" size="sm" />
            </VStack>
            <VStack>
              <Text fontSize="sm" color="gray.500">Medium</Text>
              <Logo variant="static" size="md" />
            </VStack>
            <VStack>
              <Text fontSize="sm" color="gray.500">Large</Text>
              <Logo variant="static" size="lg" />
            </VStack>
          </HStack>
        </VStack>

        <Divider />

        {/* Icon Only */}
        <VStack spacing={4}>
          <Text fontSize="lg" fontWeight="semibold">Icon Only</Text>
          <HStack spacing={8} justify="center">
            <VStack>
              <Text fontSize="sm" color="gray.500">Small</Text>
              <Logo variant="icon" size="sm" />
            </VStack>
            <VStack>
              <Text fontSize="sm" color="gray.500">Medium</Text>
              <Logo variant="icon" size="md" />
            </VStack>
            <VStack>
              <Text fontSize="sm" color="gray.500">Large</Text>
              <Logo variant="icon" size="lg" />
            </VStack>
          </HStack>
        </VStack>

        <Divider />

        {/* Animated Logo */}
        <VStack spacing={4}>
          <Text fontSize="lg" fontWeight="semibold">Animated Logo (Original)</Text>
          <Box>
            <Logo variant="animated" size="md" />
          </Box>
        </VStack>
      </VStack>
    </Box>
  );
}
