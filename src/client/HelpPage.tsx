import React, { useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  SimpleGrid,
  Card,
  CardBody,
  Icon,
  Link,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Badge,
  Button,
  useColorModeValue,
  Divider,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
} from '@chakra-ui/react';
import { Link as RouterLink } from 'react-router-dom';
import {
  FaQuestionCircle,
  FaRocket,
  FaFileAlt,
  FaEnvelopeOpenText,
  FaComments,
  FaBriefcase,
  FaGraduationCap,
  FaUser,
  FaShieldAlt,
  FaExclamationTriangle,
  FaEnvelope,
  FaBook,
  FaVideo,
  FaDownload,
  FaEdit,
  FaSearch,
  FaLinkedin,
} from 'react-icons/fa';

const HelpPage: React.FC = () => {
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const linkColor = useColorModeValue('blue.600', 'blue.400');

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const helpCategories = [
    {
      icon: FaRocket,
      title: 'Getting Started',
      description: 'Learn the basics of CareerDart and how to set up your account',
      color: 'blue.500',
    },
    {
      icon: FaFileAlt,
      title: 'Resume Builder',
      description: 'Create professional resumes with our AI-powered tools',
      color: 'green.500',
    },
    {
      icon: FaEnvelopeOpenText,
      title: 'Cover Letters',
      description: 'Generate personalized cover letters for any job application',
      color: 'purple.500',
    },
    {
      icon: FaComments,
      title: 'Interview Prep',
      description: 'Practice with AI-generated questions and improve your skills',
      color: 'orange.500',
    },
    {
      icon: FaBriefcase,
      title: 'Job Tracking',
      description: 'Organize and manage your job applications effectively',
      color: 'teal.500',
    },
    {
      icon: FaUser,
      title: 'Account & Billing',
      description: 'Manage your profile, subscription, and payment settings',
      color: 'pink.500',
    },
  ];

  const faqs = [
    {
      question: 'How accurate is the AI-generated content?',
      answer: 'Our AI generates high-quality content based on best practices and your input. However, all AI-generated content should be reviewed, customized, and verified for accuracy before use. We recommend having important documents reviewed by career professionals.',
    },
    {
      question: 'Can I customize the resume templates?',
      answer: 'Yes! Our resume builder offers full customization including colors, fonts, layouts, and styling. You can also add, remove, or modify sections to match your needs.',
    },
    {
      question: 'How does the LinkedIn integration work?',
      answer: 'Simply paste a LinkedIn job URL into our import tool, and we\'ll automatically extract the job title, company, location, and description to populate your application form.',
    },
    {
      question: 'Is my data secure and private?',
      answer: 'Absolutely. We use enterprise-grade security measures to protect your data. Your personal information is encrypted and never shared with third parties without your consent. See our Privacy Policy for details.',
    },
    {
      question: 'Can I export my resumes and cover letters?',
      answer: 'Yes, you can export your documents as PDF files optimized for both digital applications and printing. Multiple format options are available.',
    },
    {
      question: 'How do I cancel my subscription?',
      answer: 'You can cancel your subscription anytime from your Profile page under the Billing section. Your access will continue until the end of your current billing period.',
    },
  ];

  return (
    <Box minH="100vh" bg={bgColor}>
      <Container maxW="7xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <VStack spacing={4} textAlign="center">
            <Icon as={FaQuestionCircle} boxSize={12} color={linkColor} />
            <Heading as="h1" size="2xl" color={textColor}>
              Help Center
            </Heading>
            <Text fontSize="xl" color="gray.500" maxW="2xl">
              Find answers to common questions and learn how to make the most of CareerDart's AI-powered career tools.
            </Text>
          </VStack>

          {/* AI Disclaimer Alert */}
          <Alert status="warning" borderRadius="lg">
            <AlertIcon />
            <Box>
              <AlertTitle>Important: AI-Generated Content Disclaimer</AlertTitle>
              <AlertDescription>
                All AI-generated content (resumes, cover letters, interview answers) is for guidance only.
                Please review, customize, and verify accuracy before use. CareerDart does not guarantee
                job placement or interview success.
              </AlertDescription>
            </Box>
          </Alert>

          {/* Quick Help Categories */}
          <VStack spacing={6} align="stretch">
            <Heading as="h2" size="lg" color={textColor}>
              Browse Help Topics
            </Heading>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
              {helpCategories.map((category, index) => (
                <Card key={index} bg={cardBg} _hover={{ transform: 'translateY(-2px)', boxShadow: 'lg' }} transition="all 0.2s">
                  <CardBody>
                    <VStack spacing={4} align="start">
                      <Icon as={category.icon} boxSize={8} color={category.color} />
                      <VStack spacing={2} align="start">
                        <Heading as="h3" size="md" color={textColor}>
                          {category.title}
                        </Heading>
                        <Text fontSize="sm" color="gray.500">
                          {category.description}
                        </Text>
                      </VStack>
                    </VStack>
                  </CardBody>
                </Card>
              ))}
            </SimpleGrid>
          </VStack>

          {/* Quick Start Guide */}
          <VStack spacing={6} align="stretch">
            <Heading as="h2" size="lg" color={textColor}>
              Quick Start Guide
            </Heading>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
              <Card bg={cardBg}>
                <CardBody>
                  <VStack spacing={4} align="start">
                    <HStack>
                      <Icon as={FaRocket} color="blue.500" />
                      <Heading as="h3" size="md" color={textColor}>
                        New to CareerDart?
                      </Heading>
                    </HStack>
                    <VStack spacing={2} align="start" pl={6}>
                      <Text fontSize="sm">1. Create your account and complete your profile</Text>
                      <Text fontSize="sm">2. Upload or create your first resume</Text>
                      <Text fontSize="sm">3. Generate your first cover letter</Text>
                      <Text fontSize="sm">4. Start tracking job applications</Text>
                    </VStack>
                  </VStack>
                </CardBody>
              </Card>

              <Card bg={cardBg}>
                <CardBody>
                  <VStack spacing={4} align="start">
                    <HStack>
                      <Icon as={FaBook} color="green.500" />
                      <Heading as="h3" size="md" color={textColor}>
                        Best Practices
                      </Heading>
                    </HStack>
                    <VStack spacing={2} align="start" pl={6}>
                      <Text fontSize="sm">• Always review AI-generated content</Text>
                      <Text fontSize="sm">• Customize templates for each application</Text>
                      <Text fontSize="sm">• Use specific keywords from job descriptions</Text>
                      <Text fontSize="sm">• Keep your profile information updated</Text>
                    </VStack>
                  </VStack>
                </CardBody>
              </Card>
            </SimpleGrid>
          </VStack>

          {/* FAQ Section */}
          <VStack spacing={6} align="stretch">
            <Heading as="h2" size="lg" color={textColor}>
              Frequently Asked Questions
            </Heading>
            <Accordion allowToggle>
              {faqs.map((faq, index) => (
                <AccordionItem key={index} border="1px solid" borderColor={useColorModeValue('gray.200', 'gray.700')} borderRadius="lg" mb={2}>
                  <AccordionButton bg={cardBg} _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }} borderRadius="lg">
                    <Box flex="1" textAlign="left">
                      <Text fontWeight="medium" color={textColor}>
                        {faq.question}
                      </Text>
                    </Box>
                    <AccordionIcon />
                  </AccordionButton>
                  <AccordionPanel pb={4} bg={cardBg} borderRadius="lg">
                    <Text color="gray.500">{faq.answer}</Text>
                  </AccordionPanel>
                </AccordionItem>
              ))}
            </Accordion>
          </VStack>

          {/* Contact Support */}
          <Card bg={cardBg} borderColor={linkColor} borderWidth="2px">
            <CardBody>
              <VStack spacing={4} textAlign="center">
                <Icon as={FaEnvelope} boxSize={8} color={linkColor} />
                <Heading as="h3" size="lg" color={textColor}>
                  Still Need Help?
                </Heading>
                <Text color="gray.500">
                  Can't find what you're looking for? Our support team is here to help.
                </Text>
                <HStack spacing={4}>
                  <Button as="a" href="mailto:<EMAIL>" colorScheme="blue" leftIcon={<FaEnvelope />}>
                    Email Support
                  </Button>
                  <Button as={RouterLink} to="/privacy" variant="outline" colorScheme="blue">
                    Privacy Policy
                  </Button>
                  <Button as={RouterLink} to="/tos" variant="outline" colorScheme="blue">
                    Terms of Service
                  </Button>
                </HStack>
              </VStack>
            </CardBody>
          </Card>
        </VStack>
      </Container>
    </Box>
  );
};

export default HelpPage;
